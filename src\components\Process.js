"use client";

import { useRef, useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import AnimatedFixedTitle from './AnimatedFixedTitle';
import ProcessCard from './ProcessCard';

const Process = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [windowWidth, setWindowWidth] = useState(1200); // Default fallback

  useEffect(() => {
    const updateWindowWidth = () => setWindowWidth(window.innerWidth);
    updateWindowWidth(); // Set initial value
    window.addEventListener('resize', updateWindowWidth);
    return () => window.removeEventListener('resize', updateWindowWidth);
  }, []);

  // Use Framer Motion's useScroll for native scroll tracking (no smoothing)
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"],
    layoutEffect: false // Disable layout effects that might cause smoothing
  });

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const processRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Get Projects section (previous section) to determine when to show Process title
      const projectsSection = document.querySelector('[data-section="projects"]');
      if (!projectsSection) return;

      const projectsRect = projectsSection.getBoundingClientRect();
      const projectsBottom = projectsRect.bottom;
      const projectsTop = projectsRect.top;

      // Show title only when projects section is completely scrolled past
      // Since Projects animations now take 90% + 5% settling = 95% of section height,
      // we need to wait until Projects section is completely gone to avoid overlap
      const projectsCompletelyGone = projectsBottom <= -50; // Projects section completely off screen with buffer
      const projectsStartedScrolling = projectsTop <= windowHeight * 0.8;

      const shouldShowTitle = projectsCompletelyGone && projectsStartedScrolling;

      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects for title - make it fade out slower
      if (titleVisible) {
        const processTop = processRect.top;
        const triggerOffset = windowHeight * 0.1;
        
        if (processTop <= triggerOffset) {
          const scrolledIntoProcess = Math.abs(processTop - triggerOffset);
          // Increased distance so title fades out slower, allowing all cards to be seen
          const sectionScrollDistance = windowHeight * 4; // Increased from 2.5
          const scrollProgress = Math.min(1, scrolledIntoProcess / sectionScrollDistance);

          const opacity = Math.max(0, 1 - (scrollProgress * 1.5)); // Slower fade
          const blur = scrollProgress * 6; // Less blur
          const scale = Math.max(0.9, 1 - (scrollProgress * 0.2)); // Less scaling

          setScrollEffects({ opacity, blur, scale });
        } else {
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  // Process steps data
  const processSteps = [
    {
      id: 1,
      number: "01",
      title: "Initial Proposal",
      description: "We discuss your vision and I provide a detailed proposal with timeline and pricing."
    },
    {
      id: 2,
      number: "02",
      title: "Design & Planning",
      description: "I create wireframes and mockups, then plan the technical architecture for your project."
    },
    {
      id: 3,
      number: "03",
      title: "Development",
      description: "I build your project using modern technologies, keeping you updated throughout the process."
    },
    {
      id: 4,
      number: "04",
      title: "Launch & Support",
      description: "I deploy your project and provide ongoing support to ensure everything runs smoothly."
    }
  ];

  // Use a fixed section height that gives enough scroll distance
  const sectionHeight = '500vh'; // Increased to ensure last card has time to reach center

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-0">
        <AnimatedFixedTitle
          title="Process"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Process Section - Dynamic height calculated to stop when last card is centered */}
      <section
        ref={sectionRef}
        data-section="process"
        className="bg-background relative z-10"
        style={{
          height: sectionHeight, // Fixed height to ensure last card reaches center
          opacity: titleVisible ? 1 : 0, // Hide entire section until title should be visible
          pointerEvents: titleVisible ? 'auto' : 'none' // Disable interactions when hidden
        }}
      >
        {/* Process Cards Container */}
        <div className="sticky top-0 h-screen w-full overflow-hidden">
          <div className="h-full flex items-center justify-center relative bg-background">
            <ProcessCard
              processSteps={processSteps}
              scrollProgress={scrollYProgress}
            />
          </div>
        </div>
      </section>
    </>
  );
};

export default Process;
